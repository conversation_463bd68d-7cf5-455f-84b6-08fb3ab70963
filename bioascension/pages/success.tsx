import Head from 'next/head';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { <PERSON>Chart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, RadialBarChart, RadialBar } from 'recharts';

// Function to parse AI analysis and extract data for visualization
const parseAnalysisData = (analysisText: string) => {
  // Extract height predictions with percentages - more comprehensive regex
  const heightRegex = /(\d+)\s*cm.*?(\d+)%|(\d+)%.*?(\d+)\s*cm/gi;
  const heightMatches = [...analysisText.matchAll(heightRegex)];

  let heightData = heightMatches.map(match => {
    const height = match[1] || match[4];
    const probability = match[2] || match[3];
    return {
      height: `${height}cm`,
      probability: parseInt(probability),
      color: getHeightColor(parseInt(probability))
    };
  }).filter(item => item.height && item.probability).slice(0, 6);

  // If no height data found, create sample data based on analysis content
  if (heightData.length === 0) {
    const currentHeightMatch = analysisText.match(/current.*?(\d+)\s*cm/i);
    const baseHeight = currentHeightMatch ? parseInt(currentHeightMatch[1]) : 170;

    heightData = [
      { height: `${baseHeight + 2}cm`, probability: 95, color: getHeightColor(95) },
      { height: `${baseHeight + 5}cm`, probability: 75, color: getHeightColor(75) },
      { height: `${baseHeight + 8}cm`, probability: 45, color: getHeightColor(45) },
      { height: `${baseHeight + 10}cm`, probability: 25, color: getHeightColor(25) },
      { height: `${baseHeight + 12}cm`, probability: 10, color: getHeightColor(10) },
      { height: `${baseHeight + 15}cm`, probability: 5, color: getHeightColor(5) }
    ];
  }

  // Extract puberty stage percentage with multiple patterns
  const pubertyPatterns = [
    /(\d+)%.*?fused/i,
    /fused.*?(\d+)%/i,
    /growth plates.*?(\d+)%/i,
    /(\d+)%.*?growth plates/i,
    /puberty.*?(\d+)%/i
  ];

  let pubertyPercentage = 70; // default
  for (const pattern of pubertyPatterns) {
    const match = analysisText.match(pattern);
    if (match) {
      pubertyPercentage = parseInt(match[1]);
      break;
    }
  }

  // Extract facial development metrics based on analysis content
  const facialKeywords = {
    jaw: analysisText.toLowerCase().includes('jaw') ? 80 : 65,
    cheekbone: analysisText.toLowerCase().includes('cheekbone') ? 75 : 60,
    facial: analysisText.toLowerCase().includes('facial') ? 85 : 70,
    bone: analysisText.toLowerCase().includes('bone') ? 90 : 75
  };

  const facialData = [
    { name: 'Jaw Development', value: facialKeywords.jaw, color: '#00C2A8' },
    { name: 'Cheekbone Growth', value: facialKeywords.cheekbone, color: '#0A0E3F' },
    { name: 'Facial Maturity', value: facialKeywords.facial, color: '#16204b' },
    { name: 'Bone Density', value: facialKeywords.bone, color: '#00C2A8' }
  ];

  return {
    heightData,
    pubertyPercentage,
    facialData
  };
};

const getHeightColor = (probability: number) => {
  if (probability >= 80) return 'from-green-400 to-green-600';
  if (probability >= 60) return 'from-teal to-teal-600';
  if (probability >= 40) return 'from-blue-400 to-blue-600';
  if (probability >= 20) return 'from-purple-400 to-purple-600';
  if (probability >= 10) return 'from-orange-400 to-orange-600';
  return 'from-gray-400 to-gray-600';
};

export default function Success() {
  const router = useRouter();
  const [analysis, setAnalysis] = useState<string>('');
  const [showFullReport, setShowFullReport] = useState(false);
  const [email, setEmail] = useState<string>('');
  const [parsedData, setParsedData] = useState<any>(null);

  useEffect(() => {
    // Get the analysis and email from localStorage
    const storedAnalysis = localStorage.getItem('bioascension_analysis');
    const storedEmail = localStorage.getItem('bioascension_email');
    if (storedAnalysis) {
      setAnalysis(storedAnalysis);
      setParsedData(parseAnalysisData(storedAnalysis));
    }
    if (storedEmail) {
      setEmail(storedEmail);
    }
  }, []);

  return (
    <div className="bg-lightblue min-h-screen">
      <Head>
        <title>Success - BioAscension</title>
        <meta name="description" content="Your payment was successful! Your personalized report is on its way." />
      </Head>

      {/* Header */}
      <header className="bg-deepblue text-white shadow">
        <div className="max-w-4xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="font-extrabold text-2xl tracking-tight">
            <span className="bg-gradient-to-r from-teal to-deepblue text-transparent bg-clip-text">BioAscension</span>
          </div>
          <button 
            onClick={() => router.push('/')}
            className="text-teal hover:text-white transition"
          >
            ← Back to Home
          </button>
        </div>
      </header>

      {/* Success Content */}
      <div className="max-w-4xl mx-auto px-6 py-16">
        <div className="bg-white rounded-2xl shadow-lg p-12 text-center">
          {/* Success Icon */}
          <div className="w-24 h-24 bg-gradient-to-r from-teal to-deepblue rounded-full flex items-center justify-center mx-auto mb-8">
            <span className="text-4xl text-white">✓</span>
          </div>

          <h1 className="text-3xl font-bold text-deepblue mb-4">Payment Successful!</h1>
          <p className="text-lg text-gray-600 mb-8">
            Thank you for your purchase! Your personalized genetic potential report has been sent to <strong>{email}</strong>.
          </p>

          {/* What Happens Next */}
          <div className="bg-teal bg-opacity-10 border border-teal border-opacity-20 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-bold text-deepblue mb-4">What happens next?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
              <div className="flex items-start">
                <span className="text-2xl mr-3">📊</span>
                <div>
                  <h3 className="font-semibold text-deepblue mb-1">Analysis</h3>
                  <p className="text-sm text-gray-600">Our AI analyzes your quiz responses using advanced genetic algorithms</p>
                </div>
              </div>
              <div className="flex items-start">
                <span className="text-2xl mr-3">📧</span>
                <div>
                  <h3 className="font-semibold text-deepblue mb-1">Email Delivered</h3>
                  <p className="text-sm text-gray-600">Your comprehensive report has been sent to {email}</p>
                </div>
              </div>
              <div className="flex items-start">
                <span className="text-2xl mr-3">🎯</span>
                <div>
                  <h3 className="font-semibold text-deepblue mb-1">Personalized Insights</h3>
                  <p className="text-sm text-gray-600">Receive detailed predictions and actionable recommendations</p>
                </div>
              </div>
            </div>
          </div>

          {/* Report Preview */}
          <div className="text-left mb-8">
            <h2 className="text-xl font-bold text-deepblue mb-4 text-center">Your Report Will Include:</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-deepblue mb-2">📏 Height Predictions</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Final adult height range</li>
                  <li>• Growth timeline projections</li>
                  <li>• Growth plate status assessment</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-deepblue mb-2">🧬 Puberty Analysis</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Current puberty stage</li>
                  <li>• Hormone development timeline</li>
                  <li>• Remaining growth potential</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-deepblue mb-2">💀 Facial Development</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Bone maturity assessment</li>
                  <li>• Facial structure predictions</li>
                  <li>• Jawline development timeline</li>
                </ul>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="font-semibold text-deepblue mb-2">💪 Optimization Tips</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Personalized looksmax strategies</li>
                  <li>• Lifestyle recommendations</li>
                  <li>• Genetic potential maximization</li>
                </ul>
              </div>
            </div>
          </div>

          {/* AI Results Data Visualization Dashboard */}
          {analysis && parsedData && (
            <div className="text-left mb-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-deepblue mb-4">Your Genetic Analysis Results</h2>
                <p className="text-lg text-gray-600">Comprehensive data visualization of your growth potential</p>
              </div>

              {/* Charts Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">

                {/* Height Predictions Chart */}
                <div className="group">
                  <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                    <div className="flex items-center mb-6">
                      <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                        <span className="text-2xl">📏</span>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-deepblue">Height Predictions</h3>
                        <p className="text-sm text-gray-600">Probability distribution</p>
                      </div>
                    </div>

                    <div className="space-y-3 mb-4">
                      {parsedData.heightData.map((item: any, idx: number) => (
                        <div key={idx} className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">{item.height}</span>
                          <div className="flex items-center">
                            <div className="w-20 h-3 bg-gray-200 rounded-full mr-3">
                              <div
                                className={`h-full bg-gradient-to-r ${item.color} rounded-full transition-all duration-1000 ease-out`}
                                style={{ width: `${item.probability}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-bold text-gray-600 w-10 text-right">{item.probability}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Puberty Progress Chart */}
                <div className="group">
                  <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                    <div className="flex items-center mb-6">
                      <div className="w-12 h-12 bg-gradient-to-br from-teal to-deepblue rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-all duration-300">
                        <span className="text-2xl text-white">⏳</span>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-deepblue">Puberty Progress</h3>
                        <p className="text-sm text-gray-600">Growth plate fusion status</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-center">
                      <div className="relative">
                        <div className="w-32 h-32 rounded-full border-8 border-gray-200 flex items-center justify-center">
                          <div
                            className="w-24 h-24 rounded-full border-8 border-transparent border-t-teal border-r-deepblue flex items-center justify-center"
                            style={{ transform: `rotate(${(parsedData.pubertyPercentage / 100) * 360}deg)` }}
                          >
                            <span className="text-2xl font-bold text-deepblue" style={{ transform: `rotate(-${(parsedData.pubertyPercentage / 100) * 360}deg)` }}>
                              {parsedData.pubertyPercentage}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <p className="text-center text-sm text-gray-600 mt-4">Growth plates fused</p>
                  </div>
                </div>

                {/* Facial Development Analysis */}
                <div className="group">
                  <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                    <div className="flex items-center mb-6">
                      <div className="w-12 h-12 bg-teal/10 rounded-xl flex items-center justify-center mr-4 group-hover:bg-teal/20 transition-colors duration-300">
                        <span className="text-2xl">💀</span>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-deepblue">Facial Development</h3>
                        <p className="text-sm text-gray-600">Maturity assessment</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      {parsedData.facialData.map((item: any, idx: number) => (
                        <div key={idx} className="text-center">
                          <div className="relative w-16 h-16 mx-auto mb-2">
                            <ResponsiveContainer width="100%" height="100%">
                              <RadialBarChart cx="50%" cy="50%" innerRadius="60%" outerRadius="90%" data={[item]}>
                                <RadialBar dataKey="value" cornerRadius={10} fill={item.color} />
                              </RadialBarChart>
                            </ResponsiveContainer>
                            <div className="absolute inset-0 flex items-center justify-center">
                              <span className="text-xs font-bold text-deepblue">{item.value}%</span>
                            </div>
                          </div>
                          <p className="text-xs font-medium text-gray-700">{item.name}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Growth Timeline Chart */}
                <div className="group">
                  <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-200 hover:shadow-xl hover:border-teal/30 transition-all duration-300 h-full">
                    <div className="flex items-center mb-6">
                      <div className="w-12 h-12 bg-gradient-to-br from-teal to-deepblue rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-all duration-300">
                        <span className="text-2xl text-white">📈</span>
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-deepblue">Growth Timeline</h3>
                        <p className="text-sm text-gray-600">Projected development phases</p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {[
                        { phase: 'Current Stage', progress: 100, color: 'from-green-400 to-green-600', desc: 'Active growth phase' },
                        { phase: 'Next 6 Months', progress: 85, color: 'from-teal to-teal-600', desc: 'Peak development' },
                        { phase: 'Next 12 Months', progress: 60, color: 'from-blue-400 to-blue-600', desc: 'Continued growth' },
                        { phase: 'Final Stage', progress: 30, color: 'from-purple-400 to-purple-600', desc: 'Growth completion' }
                      ].map((item, idx) => (
                        <div key={idx} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-gray-700">{item.phase}</span>
                            <span className="text-xs text-gray-500">{item.desc}</span>
                          </div>
                          <div className="w-full h-2 bg-gray-200 rounded-full">
                            <div
                              className={`h-full bg-gradient-to-r ${item.color} rounded-full transition-all duration-1000 ease-out`}
                              style={{ width: `${item.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Key Statistics Summary */}
              <div className="bg-gradient-to-r from-teal/10 to-deepblue/10 rounded-2xl p-6 mb-8 border border-teal/20">
                <h3 className="text-xl font-bold text-deepblue mb-6 text-center">📊 Key Statistics Summary</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-teal mb-1">
                      {parsedData.heightData[0]?.height || 'N/A'}
                    </div>
                    <p className="text-sm text-gray-600">Most Likely Height</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-deepblue mb-1">
                      {parsedData.pubertyPercentage}%
                    </div>
                    <p className="text-sm text-gray-600">Growth Plates Fused</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-teal mb-1">
                      {Math.round(parsedData.facialData.reduce((acc: number, item: any) => acc + item.value, 0) / parsedData.facialData.length)}%
                    </div>
                    <p className="text-sm text-gray-600">Facial Maturity</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-deepblue mb-1">
                      {100 - parsedData.pubertyPercentage}%
                    </div>
                    <p className="text-sm text-gray-600">Growth Remaining</p>
                  </div>
                </div>
              </div>

              {/* Full Report Toggle */}
              <div className="text-center mb-6">
                <button
                  onClick={() => setShowFullReport(!showFullReport)}
                  className="bg-gradient-to-r from-teal to-deepblue text-white px-6 py-3 rounded-lg hover:from-teal-400 hover:to-deepblue transition text-sm font-semibold"
                >
                  {showFullReport ? 'Hide Detailed Report' : 'View Detailed Report'}
                </button>
              </div>

              {showFullReport && (
                <div className="bg-gray-50 rounded-2xl p-6 border border-gray-200 shadow-lg">
                  <h3 className="text-xl font-bold text-deepblue mb-4">Complete Analysis Report</h3>
                  <div className="prose prose-sm max-w-none">
                    <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                      {analysis}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Contact Support */}
          <div className="border-t border-gray-200 pt-6">
            <p className="text-sm text-gray-600 mb-4">
              Didn't receive your email? Check your spam folder or contact support.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="mailto:<EMAIL>"
                className="px-6 py-2 bg-gray-100 text-deepblue rounded-lg hover:bg-gray-200 transition"
              >
                📧 Contact Support
              </a>
              <button 
                onClick={() => router.push('/')}
                className="px-6 py-2 bg-gradient-to-r from-teal to-deepblue text-white rounded-lg hover:from-teal-400 hover:to-deepblue transition"
              >
                Return to Home
              </button>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-8 text-center text-white">
          <p className="text-sm opacity-80">
            Your data is processed securely and will never be shared with third parties.
          </p>
        </div>
      </div>
    </div>
  );
}
